#!/usr/bin/env python3
"""
Debug Analyzer for E-commerce Product Sourcing Agent

This utility helps analyze the exported SerpAPI responses and processed results
for debugging and optimization purposes.
"""

import os
import json
import glob
from datetime import datetime
from typing import Dict, List, Any
import argparse


def analyze_api_responses(export_dir: str = "debug_exports") -> None:
    """
    Analyze all exported API responses in the directory.
    
    Args:
        export_dir: Directory containing the exported files
    """
    print(f"🔍 Analyzing API responses in: {export_dir}")
    
    # Find all API response files
    api_files = glob.glob(os.path.join(export_dir, "serpapi_response_*.json"))
    processed_files = glob.glob(os.path.join(export_dir, "processed_results_*.json"))
    
    print(f"Found {len(api_files)} API response files")
    print(f"Found {len(processed_files)} processed result files")
    
    if not api_files:
        print("No API response files found.")
        return
    
    # Analyze each API response
    for api_file in sorted(api_files):
        print(f"\n📄 Analyzing: {os.path.basename(api_file)}")
        
        try:
            with open(api_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Extract key information
            timestamp = data.get("timestamp", "Unknown")
            search_query = data.get("search_query", "Unknown")
            raw_response = data.get("raw_response", {})
            metadata = data.get("metadata", {})
            
            print(f"   Timestamp: {timestamp}")
            print(f"   Search Query: {search_query}")
            print(f"   Total Results: {metadata.get('total_results', 0)}")
            print(f"   Has Error: {metadata.get('has_error', False)}")
            
            # Analyze search information
            search_info = raw_response.get("search_information", {})
            if search_info:
                print(f"   Amazon Total Results: {search_info.get('total_results', 'N/A')}")
                print(f"   Page: {search_info.get('page', 'N/A')}")
            
            # Analyze organic results
            organic_results = raw_response.get("organic_results", [])
            if organic_results:
                print(f"   Organic Results Count: {len(organic_results)}")
                
                # Price analysis
                prices = []
                ratings = []
                for result in organic_results:
                    if "extracted_price" in result:
                        prices.append(result["extracted_price"])
                    if "rating" in result:
                        ratings.append(result["rating"])
                
                if prices:
                    print(f"   Price Range: ${min(prices):.2f} - ${max(prices):.2f}")
                    print(f"   Average Price: ${sum(prices)/len(prices):.2f}")
                
                if ratings:
                    print(f"   Rating Range: {min(ratings):.1f} - {max(ratings):.1f}")
                    print(f"   Average Rating: {sum(ratings)/len(ratings):.1f}")
                
                # Count special attributes
                prime_count = len([r for r in organic_results if r.get("prime", False)])
                sponsored_count = len([r for r in organic_results if r.get("sponsored", False)])
                print(f"   Prime Products: {prime_count}")
                print(f"   Sponsored Products: {sponsored_count}")
            
        except Exception as e:
            print(f"   ❌ Error analyzing file: {e}")


def analyze_processed_results(export_dir: str = "debug_exports") -> None:
    """
    Analyze all processed result files in the directory.
    
    Args:
        export_dir: Directory containing the exported files
    """
    print(f"\n📊 Analyzing processed results in: {export_dir}")
    
    # Find all processed result files
    processed_files = glob.glob(os.path.join(export_dir, "processed_results_*.json"))
    
    if not processed_files:
        print("No processed result files found.")
        return
    
    # Analyze each processed result file
    for processed_file in sorted(processed_files):
        print(f"\n📄 Analyzing: {os.path.basename(processed_file)}")
        
        try:
            with open(processed_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Extract key information
            timestamp = data.get("timestamp", "Unknown")
            search_query = data.get("search_query", "Unknown")
            total_products = data.get("total_products", 0)
            summary = data.get("summary", {})
            
            print(f"   Timestamp: {timestamp}")
            print(f"   Search Query: {search_query}")
            print(f"   Total Products: {total_products}")
            
            # Analyze summary statistics
            if summary:
                price_range = summary.get("price_range", {})
                rating_range = summary.get("rating_range", {})
                
                print(f"   Price Range: ${price_range.get('min', 0):.2f} - ${price_range.get('max', 0):.2f}")
                print(f"   Average Price: ${price_range.get('avg', 0):.2f}")
                print(f"   Rating Range: {rating_range.get('min', 0):.1f} - {rating_range.get('max', 0):.1f}")
                print(f"   Average Rating: {rating_range.get('avg', 0):.1f}")
                print(f"   Prime Products: {summary.get('prime_products', 0)}")
                print(f"   Sponsored Products: {summary.get('sponsored_products', 0)}")
            
            # Analyze individual products
            products = data.get("processed_products", [])
            if products:
                print(f"   Product Details:")
                for i, product in enumerate(products, 1):
                    title = product.get("title", "Unknown")[:60] + "..."
                    price = product.get("price", 0)
                    rating = product.get("rating", 0)
                    reviews = product.get("reviews_count", 0)
                    asin = product.get("asin", "N/A")
                    print(f"     {i}. {title}")
                    print(f"        Price: ${price}, Rating: {rating} ({reviews} reviews), ASIN: {asin}")
            
        except Exception as e:
            print(f"   ❌ Error analyzing file: {e}")


def clean_old_exports(export_dir: str = "debug_exports", days_old: int = 7) -> None:
    """
    Clean up old export files.
    
    Args:
        export_dir: Directory containing the exported files
        days_old: Remove files older than this many days
    """
    print(f"\n🧹 Cleaning exports older than {days_old} days...")
    
    all_files = glob.glob(os.path.join(export_dir, "*.json"))
    current_time = datetime.now()
    removed_count = 0
    
    for file_path in all_files:
        try:
            file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            age_days = (current_time - file_time).days
            
            if age_days > days_old:
                os.remove(file_path)
                print(f"   Removed: {os.path.basename(file_path)} (age: {age_days} days)")
                removed_count += 1
        except Exception as e:
            print(f"   ❌ Error removing {file_path}: {e}")
    
    print(f"✅ Removed {removed_count} old files")


def main():
    """Main function to run the debug analyzer."""
    parser = argparse.ArgumentParser(description="Analyze SerpAPI debug exports")
    parser.add_argument("--dir", default="debug_exports", help="Export directory to analyze")
    parser.add_argument("--clean", type=int, metavar="DAYS", help="Clean files older than DAYS")
    parser.add_argument("--api-only", action="store_true", help="Analyze only API responses")
    parser.add_argument("--processed-only", action="store_true", help="Analyze only processed results")
    
    args = parser.parse_args()
    
    print("🔍 E-commerce Agent Debug Analyzer")
    print("=" * 50)
    
    if args.clean:
        clean_old_exports(args.dir, args.clean)
        return
    
    if not args.processed_only:
        analyze_api_responses(args.dir)
    
    if not args.api_only:
        analyze_processed_results(args.dir)
    
    print("\n✅ Analysis complete!")


if __name__ == "__main__":
    main()
