"""
E-commerce Product Sourcing Agent

This agent helps users find hot-selling products on Amazon and corresponding suppliers on 1688 platform.
It provides comprehensive information including wholesale prices, supplier details, and shipping costs.

Architecture:
- Single agent using Strands framework
- Two custom tools: Amazon hot products API and 1688 supplier platform API
- Structured workflow for product sourcing and supplier matching
"""

import os
import json
import re
import requests
from datetime import datetime
from typing import Any, Dict, List, Optional
from dataclasses import dataclass

from strands import Agent, tool
from strands.models import BedrockModel
from strands.types.tools import ToolResult, ToolUse

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded from .env file")
except ImportError:
    print("Warning: python-dotenv not installed. Install with: pip install python-dotenv")

# SerpAPI for Amazon search integration
try:
    from serpapi import GoogleSearch
except ImportError:
    print("Warning: serpapi not installed. Install with: pip install google-search-results")
    GoogleSearch = None


def _export_api_response(response_data: Dict[str, Any], search_query: str, export_dir: str = None) -> str:
    """
    Export SerpAPI response to a JSON file for debugging purposes.

    Args:
        response_data: The raw SerpAPI response
        search_query: The search query used
        export_dir: Directory to save the export files (uses env var if None)

    Returns:
        Path to the exported file
    """
    # Check if export is enabled
    if not os.getenv("EXPORT_API_RESPONSES", "false").lower() == "true":
        return ""

    try:
        # Use environment variable for export directory if not specified
        if export_dir is None:
            export_dir = os.getenv("DEBUG_EXPORT_DIR", "debug_exports")
        # Create export directory if it doesn't exist
        os.makedirs(export_dir, exist_ok=True)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_query = re.sub(r'[^\w\s-]', '', search_query).strip()
        safe_query = re.sub(r'[-\s]+', '_', safe_query)
        filename = f"serpapi_response_{safe_query}_{timestamp}.json"
        filepath = os.path.join(export_dir, filename)

        # Prepare export data
        export_data = {
            "timestamp": datetime.now().isoformat(),
            "search_query": search_query,
            "raw_response": response_data,
            "metadata": {
                "total_results": len(response_data.get("organic_results", [])),
                "has_error": "error" in response_data,
                "search_parameters": response_data.get("search_parameters", {}),
                "search_information": response_data.get("search_information", {})
            }
        }

        # Write to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        print(f"📁 API response exported to: {filepath}")
        return filepath

    except Exception as e:
        print(f"⚠️  Failed to export API response: {e}")
        return ""


def _export_processed_results(processed_products: List[Dict[str, Any]], search_query: str, export_dir: str = None) -> str:
    """
    Export processed product results to a JSON file for debugging purposes.

    Args:
        processed_products: The processed product list
        search_query: The search query used
        export_dir: Directory to save the export files (uses env var if None)

    Returns:
        Path to the exported file
    """
    # Check if export is enabled
    if not os.getenv("EXPORT_API_RESPONSES", "false").lower() == "true":
        return ""

    try:
        # Use environment variable for export directory if not specified
        if export_dir is None:
            export_dir = os.getenv("DEBUG_EXPORT_DIR", "debug_exports")
        # Create export directory if it doesn't exist
        os.makedirs(export_dir, exist_ok=True)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_query = re.sub(r'[^\w\s-]', '', search_query).strip()
        safe_query = re.sub(r'[-\s]+', '_', safe_query)
        filename = f"processed_results_{safe_query}_{timestamp}.json"
        filepath = os.path.join(export_dir, filename)

        # Prepare export data
        export_data = {
            "timestamp": datetime.now().isoformat(),
            "search_query": search_query,
            "total_products": len(processed_products),
            "processed_products": processed_products,
            "summary": {
                "price_range": {
                    "min": min([p.get("price", 0) for p in processed_products]) if processed_products else 0,
                    "max": max([p.get("price", 0) for p in processed_products]) if processed_products else 0,
                    "avg": sum([p.get("price", 0) for p in processed_products]) / len(processed_products) if processed_products else 0
                },
                "rating_range": {
                    "min": min([p.get("rating", 0) for p in processed_products]) if processed_products else 0,
                    "max": max([p.get("rating", 0) for p in processed_products]) if processed_products else 0,
                    "avg": sum([p.get("rating", 0) for p in processed_products]) / len(processed_products) if processed_products else 0
                },
                "prime_products": len([p for p in processed_products if p.get("prime", False)]),
                "sponsored_products": len([p for p in processed_products if p.get("sponsored", False)])
            }
        }

        # Write to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        print(f"📊 Processed results exported to: {filepath}")
        return filepath

    except Exception as e:
        print(f"⚠️  Failed to export processed results: {e}")
        return ""


@dataclass
class ProductInfo:
    """Data class for product information"""
    title: str
    price: float
    rating: float
    reviews_count: int
    image_url: str
    product_url: str
    category: str
    sales_rank: Optional[int] = None
    keywords: List[str] = None


@dataclass
class SupplierInfo:
    """Data class for supplier information"""
    supplier_name: str
    company_name: str
    wholesale_price: float
    minimum_order_quantity: int
    supplier_rating: float
    years_in_business: int
    location: str
    contact_info: Dict[str, str]
    product_images: List[str]
    shipping_cost: float
    delivery_time: str
    certifications: List[str] = None


# Tool 1: Amazon Hot Products API Integration
@tool
def search_amazon_hot_products(
    product_category: str,
    keywords: str,
    price_range_min: float = 0.0,
    price_range_max: float = 1000.0,
    min_rating: float = 4.0,
    max_results: int = 10
) -> List[Dict[str, Any]]:
    """
    Search for hot-selling products on Amazon based on user requirements using SerpAPI.

    Args:
        product_category: Product category (e.g., "electronics", "home", "fashion")
        keywords: Search keywords describing the product
        price_range_min: Minimum price filter
        price_range_max: Maximum price filter
        min_rating: Minimum product rating filter
        max_results: Maximum number of results to return

    Returns:
        List of hot-selling products with details including price, rating, sales data
    """

    print(f"🔍 Searching Amazon for hot products: {keywords} in {product_category}")
    print(f"   Price range: ${price_range_min} - ${price_range_max}")
    print(f"   Minimum rating: {min_rating}")

    # Check if SerpAPI is available
    if GoogleSearch is None:
        print("⚠️  SerpAPI not available, falling back to mock data")
        return _get_mock_amazon_products(keywords, product_category, price_range_min, price_range_max, min_rating, max_results)

    try:
        # Get API key from environment variable
        api_key = os.getenv("SERPAPI_KEY")
        if not api_key:
            print("⚠️  SERPAPI_KEY environment variable not set, falling back to mock data")
            return _get_mock_amazon_products(keywords, product_category, price_range_min, price_range_max, min_rating, max_results)

        # Prepare SerpAPI parameters
        search_query = f"{keywords} {product_category}".strip()
        params = {
            "engine": "amazon",
            "k": search_query,  # k parameter represents the product keyword
            "api_key": api_key,
            "num": min(max_results * 2, 20)  # Get more results to filter
        }

        # Execute search
        search = GoogleSearch(params)
        results = search.get_dict()

        # Export API response for debugging
        export_path = _export_api_response(results, search_query)

        # Check for errors
        if "error" in results:
            print(f"❌ SerpAPI error: {results['error']}")
            if export_path:
                print(f"   Error details saved to: {export_path}")
            return _get_mock_amazon_products(keywords, product_category, price_range_min, price_range_max, min_rating, max_results)

        # Extract organic results
        organic_results = results.get("organic_results", [])
        if not organic_results:
            print("⚠️  No organic results found, falling back to mock data")
            return _get_mock_amazon_products(keywords, product_category, price_range_min, price_range_max, min_rating, max_results)

        # Transform SerpAPI results to our format
        transformed_products = []
        for result in organic_results:
            try:
                product = _transform_serpapi_result(result, product_category, keywords)
                if product and _meets_criteria(product, price_range_min, price_range_max, min_rating):
                    transformed_products.append(product)
                    if len(transformed_products) >= max_results:
                        break
            except Exception as e:
                print(f"⚠️  Error processing result: {e}")
                continue

        print(f"✅ Found {len(transformed_products)} products matching criteria")

        # Export processed results for debugging
        if transformed_products:
            _export_processed_results(transformed_products, search_query)

        return transformed_products

    except Exception as e:
        print(f"❌ SerpAPI search failed: {e}")
        return _get_mock_amazon_products(keywords, product_category, price_range_min, price_range_max, min_rating, max_results)


def _transform_serpapi_result(result: Dict[str, Any], product_category: str, keywords: str) -> Optional[Dict[str, Any]]:
    """
    Transform a SerpAPI result into our product format.

    Args:
        result: Single organic result from SerpAPI
        product_category: Product category for context
        keywords: Search keywords for context

    Returns:
        Transformed product dictionary or None if invalid
    """
    try:
        # Extract basic information
        title = result.get("title", "")
        if not title:
            return None

        # Extract price information
        price = result.get("extracted_price")
        if price is None:
            # Try to extract from price string
            price_str = result.get("price", "")
            if price_str:
                # Extract numeric value from price string like "$29.99"
                price_match = re.search(r'\$?(\d+\.?\d*)', price_str)
                if price_match:
                    price = float(price_match.group(1))
                else:
                    price = 0.0
            else:
                price = 0.0

        # Extract rating and reviews
        rating = result.get("rating", 0.0)
        reviews_count = result.get("reviews", 0)

        # Extract other information
        asin = result.get("asin", "")
        thumbnail = result.get("thumbnail", "")
        link_clean = result.get("link_clean", result.get("link", ""))

        # Extract sales indicators
        bought_last_month = result.get("bought_last_month", "")
        monthly_sales = _extract_monthly_sales(bought_last_month)

        # Determine trend based on available data
        trend = "stable"
        if "best seller" in title.lower() or "bestseller" in title.lower():
            trend = "increasing"
        elif monthly_sales > 1000:
            trend = "increasing"

        # Determine competition level based on reviews
        competition_level = "medium"
        if reviews_count > 5000:
            competition_level = "high"
        elif reviews_count < 500:
            competition_level = "low"

        # Build product dictionary
        product = {
            "title": title,
            "price": price,
            "rating": rating,
            "reviews_count": reviews_count,
            "image_url": thumbnail,
            "product_url": link_clean,
            "category": product_category,
            "asin": asin,
            "keywords": keywords.split(),
            "monthly_sales": monthly_sales,
            "trend": trend,
            "competition_level": competition_level,
            "bought_last_month": bought_last_month,
            "prime": result.get("prime", False),
            "sponsored": result.get("sponsored", False)
        }

        return product

    except Exception as e:
        print(f"⚠️  Error transforming result: {e}")
        return None


def _extract_monthly_sales(bought_text: str) -> int:
    """
    Extract monthly sales number from text like "200+ bought in past month".

    Args:
        bought_text: Text containing sales information

    Returns:
        Estimated monthly sales number
    """
    if not bought_text:
        return 0

    # Look for patterns like "200+ bought", "1K+ bought", etc.
    match = re.search(r'(\d+(?:\.\d+)?)\s*([KkMm]?)\+?\s*bought', bought_text)
    if match:
        number = float(match.group(1))
        multiplier = match.group(2).upper()

        if multiplier == 'K':
            number *= 1000
        elif multiplier == 'M':
            number *= 1000000

        return int(number)

    return 0


def _meets_criteria(product: Dict[str, Any], price_min: float, price_max: float, min_rating: float) -> bool:
    """
    Check if a product meets the specified criteria.

    Args:
        product: Product dictionary
        price_min: Minimum price
        price_max: Maximum price
        min_rating: Minimum rating

    Returns:
        True if product meets criteria
    """
    price = product.get("price", 0)
    rating = product.get("rating", 0)

    return (price_min <= price <= price_max) and (rating >= min_rating)


def _get_mock_amazon_products(keywords: str, product_category: str, price_min: float, price_max: float, min_rating: float, max_results: int) -> List[Dict[str, Any]]:
    """
    Fallback function that returns mock Amazon products when SerpAPI is not available.

    Args:
        keywords: Search keywords
        product_category: Product category
        price_min: Minimum price filter
        price_max: Maximum price filter
        min_rating: Minimum rating filter
        max_results: Maximum number of results

    Returns:
        List of mock products
    """
    # Simulated hot products data
    mock_products = [
        {
            "title": f"Premium {keywords} - Best Seller",
            "price": 29.99,
            "rating": 4.5,
            "reviews_count": 2847,
            "image_url": "https://example.com/product1.jpg",
            "product_url": "https://amazon.com/product1",
            "category": product_category,
            "asin": "B0MOCK001",
            "keywords": keywords.split(),
            "monthly_sales": 15000,
            "trend": "increasing",
            "competition_level": "medium",
            "bought_last_month": "15K+ bought in past month",
            "prime": True,
            "sponsored": False
        },
        {
            "title": f"Professional {keywords} Kit",
            "price": 45.99,
            "rating": 4.7,
            "reviews_count": 1923,
            "image_url": "https://example.com/product2.jpg",
            "product_url": "https://amazon.com/product2",
            "category": product_category,
            "asin": "B0MOCK002",
            "keywords": keywords.split(),
            "monthly_sales": 12000,
            "trend": "stable",
            "competition_level": "low",
            "bought_last_month": "12K+ bought in past month",
            "prime": True,
            "sponsored": True
        },
        {
            "title": f"Deluxe {keywords} Set",
            "price": 19.99,
            "rating": 4.3,
            "reviews_count": 3456,
            "image_url": "https://example.com/product3.jpg",
            "product_url": "https://amazon.com/product3",
            "category": product_category,
            "asin": "B0MOCK003",
            "keywords": keywords.split(),
            "monthly_sales": 8500,
            "trend": "increasing",
            "competition_level": "high",
            "bought_last_month": "8.5K+ bought in past month",
            "prime": False,
            "sponsored": False
        }
    ]

    # Filter by price range and rating
    filtered_products = [
        product for product in mock_products
        if (price_min <= product["price"] <= price_max and
            product["rating"] >= min_rating)
    ]

    return filtered_products[:max_results]


def _export_1688_api_response(response_data: Dict[str, Any], search_query: str, image_url: str, export_dir: str = None) -> str:
    """
    Export 1688 API response to a JSON file for debugging purposes.

    Args:
        response_data: The raw 1688 API response
        search_query: The search query used
        image_url: The image URL used for search
        export_dir: Directory to save the export files (uses env var if None)

    Returns:
        Path to the exported file
    """
    # Check if export is enabled
    if not os.getenv("EXPORT_API_RESPONSES", "false").lower() == "true":
        return ""

    try:
        # Use environment variable for export directory if not specified
        if export_dir is None:
            export_dir = os.getenv("DEBUG_EXPORT_DIR", "debug_exports")

        # Create export directory if it doesn't exist
        os.makedirs(export_dir, exist_ok=True)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_query = re.sub(r'[^\w\s-]', '', search_query).strip()
        safe_query = re.sub(r'[-\s]+', '_', safe_query)
        filename = f"1688_api_response_{safe_query}_{timestamp}.json"
        filepath = os.path.join(export_dir, filename)

        # Prepare export data
        export_data = {
            "timestamp": datetime.now().isoformat(),
            "search_query": search_query,
            "image_url": image_url,
            "raw_response": response_data,
            "metadata": {
                "total_results": response_data.get("data", {}).get("total_count", 0) if response_data.get("code") == 200 else 0,
                "has_error": response_data.get("code", 500) != 200,
                "page": response_data.get("data", {}).get("page", 1),
                "page_size": response_data.get("data", {}).get("page_size", 0),
                "api_status": response_data.get("msg", "unknown")
            }
        }

        # Write to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        print(f"📁 1688 API response exported to: {filepath}")
        return filepath

    except Exception as e:
        print(f"⚠️  Failed to export 1688 API response: {e}")
        return ""


def _transform_1688_result(item: Dict[str, Any], target_price: float) -> Optional[Dict[str, Any]]:
    """
    Transform a 1688 API result into our supplier format.

    Args:
        item: Single item from 1688 API response
        target_price: Target wholesale price for context

    Returns:
        Transformed supplier dictionary or None if invalid
    """
    try:
        # Extract basic information
        title = item.get("title", "")
        if not title:
            return None

        # Extract price information
        price_info = item.get("price_info", {})
        wholesale_price = float(price_info.get("wholesale_price", item.get("price", "0")))

        # Extract shop information
        shop_info = item.get("shop_info", {})
        supplier_name = shop_info.get("login_id", "Unknown Supplier")
        company_name = shop_info.get("company_name", "Unknown Company")

        # Extract location
        location_list = shop_info.get("location", [])
        location = ", ".join(location_list) if location_list else "Unknown Location"

        # Extract minimum order quantity
        quantity_begin = item.get("quantity_begin", 1)
        quantity_prices = item.get("quantity_prices", [])
        min_order_qty = quantity_begin
        if quantity_prices and len(quantity_prices) > 0:
            min_order_qty = int(quantity_prices[0].get("begin_num", quantity_begin))

        # Calculate supplier rating based on available metrics
        goods_score = item.get("goods_score", 3)
        tp_year = shop_info.get("tp_year", 1)
        is_factory = shop_info.get("is_factory", False)

        # Create a composite rating (1-5 scale)
        supplier_rating = min(5.0, max(1.0, (goods_score + (tp_year * 0.1) + (1 if is_factory else 0)) / 2))

        # Extract delivery information
        delivery_info = item.get("delivery_info", {})
        area_from = delivery_info.get("area_from", [])
        delivery_location = ", ".join(area_from) if area_from else location

        # Estimate shipping cost based on location and price
        shipping_cost = _estimate_shipping_cost(wholesale_price, delivery_location)

        # Estimate delivery time
        delivery_time = _estimate_delivery_time(delivery_location)

        # Extract certifications (inferred from shop type and factory status)
        certifications = []
        if is_factory:
            certifications.append("Factory Direct")
        if shop_info.get("tp_member", False):
            certifications.append("TP Member")
        if shop_info.get("factory_inspection", False):
            certifications.append("Factory Inspected")

        # Build supplier dictionary
        supplier = {
            "supplier_name": supplier_name,
            "company_name": company_name,
            "wholesale_price": wholesale_price,
            "minimum_order_quantity": min_order_qty,
            "supplier_rating": round(supplier_rating, 1),
            "years_in_business": tp_year,
            "location": location,
            "contact_info": {
                "shop_url": shop_info.get("shop_url", ""),
                "member_id": shop_info.get("member_id", ""),
                "login_id": supplier_name
            },
            "product_images": [item.get("img", "")],
            "shipping_cost": shipping_cost,
            "delivery_time": delivery_time,
            "certifications": certifications,
            "trade_assurance": shop_info.get("tp_member", False),
            "response_rate": "N/A",  # Not available in API response
            "product_url": item.get("product_url", ""),
            "item_id": str(item.get("item_id", "")),
            "biz_type": shop_info.get("biz_type", "Unknown"),
            "is_factory": is_factory,
            "goods_score": goods_score
        }

        return supplier

    except Exception as e:
        print(f"⚠️  Error transforming 1688 result: {e}")
        return None


def _estimate_shipping_cost(wholesale_price: float, location: str) -> float:
    """
    Estimate shipping cost based on wholesale price and location.

    Args:
        wholesale_price: Wholesale price of the product
        location: Supplier location

    Returns:
        Estimated shipping cost per unit
    """
    # Base shipping cost
    base_cost = 2.0

    # Adjust based on price (higher value items have higher shipping)
    price_factor = min(wholesale_price * 0.1, 5.0)

    # Adjust based on location (some regions have higher shipping costs)
    location_factor = 1.0
    if "广东" in location or "浙江" in location:  # Major manufacturing regions
        location_factor = 0.8
    elif "新疆" in location or "西藏" in location:  # Remote regions
        location_factor = 1.5

    return round(base_cost + price_factor * location_factor, 2)


def _estimate_delivery_time(location: str) -> str:
    """
    Estimate delivery time based on supplier location.

    Args:
        location: Supplier location

    Returns:
        Estimated delivery time string
    """
    # Default delivery time
    if "广东" in location or "浙江" in location or "江苏" in location:
        return "7-12 days"
    elif "上海" in location or "北京" in location:
        return "5-10 days"
    elif "新疆" in location or "西藏" in location or "内蒙古" in location:
        return "15-25 days"
    else:
        return "10-15 days"


def _meets_1688_criteria(supplier: Dict[str, Any], target_price: float, min_order_qty: int, location_pref: str) -> bool:
    """
    Check if a 1688 supplier meets the specified criteria.

    Args:
        supplier: Supplier dictionary
        target_price: Target wholesale price
        min_order_qty: Minimum order quantity requirement
        location_pref: Preferred supplier location

    Returns:
        True if supplier meets criteria
    """
    # Check price (allow some flexibility - up to 50% above target)
    price_ok = supplier.get("wholesale_price", float('inf')) <= target_price * 1.5

    # Check minimum order quantity (allow up to 3x the requirement)
    moq_ok = supplier.get("minimum_order_quantity", float('inf')) <= min_order_qty * 3

    # Check location preference
    location_ok = True
    if location_pref and location_pref.lower() != "any":
        supplier_location = supplier.get("location", "").lower()
        location_ok = location_pref.lower() in supplier_location

    return price_ok and moq_ok and location_ok


# Tool 2: 1688 Supplier Platform API Integration
@tool
def find_1688_suppliers(
    product_title: str,
    target_price: float,
    minimum_order_qty: int = 100,
    supplier_location: str = "any",
    max_suppliers: int = 5
) -> List[Dict[str, Any]]:
    """
    Find suppliers on 1688 platform for the specified product using TMAPI image search.

    Args:
        product_title: Title/description of the product to source
        target_price: Target wholesale price per unit
        minimum_order_qty: Minimum order quantity requirement
        supplier_location: Preferred supplier location (e.g., "Guangdong", "Zhejiang", "any")
        max_suppliers: Maximum number of suppliers to return

    Returns:
        List of suppliers with wholesale prices, MOQ, ratings, and contact information
    """

    print(f"🏭 Searching 1688 suppliers for: {product_title}")
    print(f"   Target price: ${target_price}")
    print(f"   Minimum order quantity: {minimum_order_qty}")
    print(f"   Location preference: {supplier_location}")

    # Get API token from environment
    api_token = os.getenv("TMAPI_1688_TOKEN")
    if not api_token:
        print("⚠️  TMAPI_1688_TOKEN environment variable not set, falling back to mock data")
        return _get_mock_1688_suppliers(product_title, target_price, minimum_order_qty, supplier_location, max_suppliers)

    try:
        # For image-based search, we need a product image URL
        # In a real implementation, this would come from the Amazon product search results
        # For now, we'll use a placeholder or try to find a related image
        image_url = _get_product_image_url(product_title)

        if not image_url:
            print("⚠️  No product image available for search, falling back to mock data")
            return _get_mock_1688_suppliers(product_title, target_price, minimum_order_qty, supplier_location, max_suppliers)

        # Prepare TMAPI request parameters
        url = "http://api.tmapi.top/1688/search/image"
        querystring = {
            "apiToken": api_token,
            "img_url": image_url,
            "page": 1,
            "page_size": min(max_suppliers * 3, 20),  # Get more results to filter
            "sort": "default"
        }

        print(f"🔍 Searching 1688 with image: {image_url[:60]}...")

        # Execute API request
        response = requests.get(url, params=querystring, timeout=30)
        response.raise_for_status()

        # Parse response
        api_data = response.json()

        # Export API response for debugging
        export_path = _export_1688_api_response(api_data, product_title, image_url)

        # Check for API errors
        if api_data.get("code") != 200:
            error_msg = api_data.get("msg", "Unknown error")
            print(f"❌ 1688 API error: {error_msg}")
            if export_path:
                print(f"   Error details saved to: {export_path}")
            return _get_mock_1688_suppliers(product_title, target_price, minimum_order_qty, supplier_location, max_suppliers)

        # Extract items from response
        data = api_data.get("data", {})
        items = data.get("items", [])

        if not items:
            print("⚠️  No suppliers found in 1688 API response, falling back to mock data")
            return _get_mock_1688_suppliers(product_title, target_price, minimum_order_qty, supplier_location, max_suppliers)

        print(f"📦 Found {len(items)} potential suppliers from 1688 API")

        # Transform API results to our format
        transformed_suppliers = []
        for item in items:
            try:
                supplier = _transform_1688_result(item, target_price)
                if supplier and _meets_1688_criteria(supplier, target_price, minimum_order_qty, supplier_location):
                    transformed_suppliers.append(supplier)
                    if len(transformed_suppliers) >= max_suppliers:
                        break
            except Exception as e:
                print(f"⚠️  Error processing 1688 supplier: {e}")
                continue

        # Sort by rating and price
        transformed_suppliers.sort(key=lambda x: (-x["supplier_rating"], x["wholesale_price"]))

        print(f"✅ Found {len(transformed_suppliers)} suppliers matching criteria")

        # Export processed results for debugging
        if transformed_suppliers:
            _export_1688_processed_results(transformed_suppliers, product_title, image_url)

        return transformed_suppliers

    except requests.exceptions.RequestException as e:
        print(f"❌ 1688 API request failed: {e}")
        return _get_mock_1688_suppliers(product_title, target_price, minimum_order_qty, supplier_location, max_suppliers)
    except Exception as e:
        print(f"❌ 1688 API search failed: {e}")
        return _get_mock_1688_suppliers(product_title, target_price, minimum_order_qty, supplier_location, max_suppliers)


def _get_product_image_url(product_title: str) -> Optional[str]:
    """
    Get a product image URL for 1688 search. In a real implementation, this would
    come from the Amazon search results. For now, we'll use a placeholder approach.

    Args:
        product_title: Product title to search for

    Returns:
        Image URL or None if not available
    """
    # In a real implementation, you would:
    # 1. Use the image_url from Amazon search results
    # 2. Store the Amazon results and pass them to this function
    # 3. Or implement a product image search service

    # For demonstration, we'll use a placeholder image or return None
    # This forces the function to fall back to mock data for testing

    # You could also implement a simple image search here:
    # - Search for product images using Google Images API
    # - Use a product database with known image URLs
    # - Extract images from e-commerce sites

    print(f"⚠️  Image URL lookup not implemented for: {product_title}")
    print("   In production, this would use Amazon product images from previous search")

    return None  # This will trigger fallback to mock data


def _export_1688_processed_results(suppliers: List[Dict[str, Any]], search_query: str, image_url: str, export_dir: str = None) -> str:
    """
    Export processed 1688 supplier results to a JSON file for debugging purposes.

    Args:
        suppliers: The processed supplier list
        search_query: The search query used
        image_url: The image URL used for search
        export_dir: Directory to save the export files (uses env var if None)

    Returns:
        Path to the exported file
    """
    # Check if export is enabled
    if not os.getenv("EXPORT_API_RESPONSES", "false").lower() == "true":
        return ""

    try:
        # Use environment variable for export directory if not specified
        if export_dir is None:
            export_dir = os.getenv("DEBUG_EXPORT_DIR", "debug_exports")

        # Create export directory if it doesn't exist
        os.makedirs(export_dir, exist_ok=True)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_query = re.sub(r'[^\w\s-]', '', search_query).strip()
        safe_query = re.sub(r'[-\s]+', '_', safe_query)
        filename = f"1688_processed_results_{safe_query}_{timestamp}.json"
        filepath = os.path.join(export_dir, filename)

        # Prepare export data
        export_data = {
            "timestamp": datetime.now().isoformat(),
            "search_query": search_query,
            "image_url": image_url,
            "total_suppliers": len(suppliers),
            "processed_suppliers": suppliers,
            "summary": {
                "price_range": {
                    "min": min([s.get("wholesale_price", 0) for s in suppliers]) if suppliers else 0,
                    "max": max([s.get("wholesale_price", 0) for s in suppliers]) if suppliers else 0,
                    "avg": sum([s.get("wholesale_price", 0) for s in suppliers]) / len(suppliers) if suppliers else 0
                },
                "rating_range": {
                    "min": min([s.get("supplier_rating", 0) for s in suppliers]) if suppliers else 0,
                    "max": max([s.get("supplier_rating", 0) for s in suppliers]) if suppliers else 0,
                    "avg": sum([s.get("supplier_rating", 0) for s in suppliers]) / len(suppliers) if suppliers else 0
                },
                "moq_range": {
                    "min": min([s.get("minimum_order_quantity", 0) for s in suppliers]) if suppliers else 0,
                    "max": max([s.get("minimum_order_quantity", 0) for s in suppliers]) if suppliers else 0,
                    "avg": sum([s.get("minimum_order_quantity", 0) for s in suppliers]) / len(suppliers) if suppliers else 0
                },
                "factory_suppliers": len([s for s in suppliers if s.get("is_factory", False)]),
                "trade_assurance_suppliers": len([s for s in suppliers if s.get("trade_assurance", False)])
            }
        }

        # Write to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        print(f"📊 1688 processed results exported to: {filepath}")
        return filepath

    except Exception as e:
        print(f"⚠️  Failed to export 1688 processed results: {e}")
        return ""


def _get_mock_1688_suppliers(product_title: str, target_price: float, minimum_order_qty: int, supplier_location: str, max_suppliers: int) -> List[Dict[str, Any]]:
    """
    Fallback function that returns mock 1688 suppliers when API is not available.

    Args:
        product_title: Product title
        target_price: Target wholesale price
        minimum_order_qty: Minimum order quantity
        supplier_location: Preferred location
        max_suppliers: Maximum number of suppliers

    Returns:
        List of mock suppliers
    """
    print("📋 Using mock 1688 supplier data")

    # Simulated supplier data
    mock_suppliers = [
        {
            "supplier_name": "Golden Dragon Manufacturing",
            "company_name": "Shenzhen Golden Dragon Electronics Co., Ltd.",
            "wholesale_price": target_price * 0.3,  # 30% of target price
            "minimum_order_quantity": 500,
            "supplier_rating": 4.8,
            "years_in_business": 12,
            "location": "Shenzhen, Guangdong",
            "contact_info": {
                "shop_url": "https://shop1234.1688.com",
                "member_id": "b2b-golden-dragon",
                "login_id": "Golden Dragon Manufacturing"
            },
            "product_images": [
                "https://1688.com/supplier1/image1.jpg",
                "https://1688.com/supplier1/image2.jpg"
            ],
            "shipping_cost": 2.50,
            "delivery_time": "15-20 days",
            "certifications": ["Factory Direct", "TP Member"],
            "trade_assurance": True,
            "response_rate": "98%",
            "product_url": "https://detail.1688.com/offer/*********.html",
            "item_id": "*********",
            "biz_type": "生产加工",
            "is_factory": True,
            "goods_score": 5
        },
        {
            "supplier_name": "Sunrise Industrial",
            "company_name": "Yiwu Sunrise Trading Co., Ltd.",
            "wholesale_price": target_price * 0.35,  # 35% of target price
            "minimum_order_quantity": 300,
            "supplier_rating": 4.6,
            "years_in_business": 8,
            "location": "Yiwu, Zhejiang",
            "contact_info": {
                "shop_url": "https://shop5678.1688.com",
                "member_id": "b2b-sunrise-trading",
                "login_id": "Sunrise Industrial"
            },
            "product_images": [
                "https://1688.com/supplier2/image1.jpg",
                "https://1688.com/supplier2/image2.jpg"
            ],
            "shipping_cost": 3.00,
            "delivery_time": "12-18 days",
            "certifications": ["TP Member"],
            "trade_assurance": True,
            "response_rate": "95%",
            "product_url": "https://detail.1688.com/offer/*********.html",
            "item_id": "*********",
            "biz_type": "贸易型",
            "is_factory": False,
            "goods_score": 4
        },
        {
            "supplier_name": "Phoenix Manufacturing",
            "company_name": "Dongguan Phoenix Electronics Factory",
            "wholesale_price": target_price * 0.28,  # 28% of target price
            "minimum_order_quantity": 1000,
            "supplier_rating": 4.9,
            "years_in_business": 15,
            "location": "Dongguan, Guangdong",
            "contact_info": {
                "shop_url": "https://shop9999.1688.com",
                "member_id": "b2b-phoenix-electronics",
                "login_id": "Phoenix Manufacturing"
            },
            "product_images": [
                "https://1688.com/supplier3/image1.jpg",
                "https://1688.com/supplier3/image2.jpg"
            ],
            "shipping_cost": 2.20,
            "delivery_time": "10-15 days",
            "certifications": ["Factory Direct", "TP Member", "Factory Inspected"],
            "trade_assurance": True,
            "response_rate": "99%",
            "product_url": "https://detail.1688.com/offer/*********.html",
            "item_id": "*********",
            "biz_type": "生产加工",
            "is_factory": True,
            "goods_score": 5
        }
    ]

    # Filter suppliers based on criteria
    filtered_suppliers = [
        supplier for supplier in mock_suppliers
        if (supplier["minimum_order_quantity"] <= minimum_order_qty * 2 and  # Allow some flexibility
            (supplier_location == "any" or supplier_location.lower() in supplier["location"].lower()))
    ]

    # Sort by rating and price
    filtered_suppliers.sort(key=lambda x: (-x["supplier_rating"], x["wholesale_price"]))

    return filtered_suppliers[:max_suppliers]


def create_ecommerce_sourcing_agent() -> Agent:
    """
    Create and configure the e-commerce product sourcing agent.
    
    Returns:
        Configured Strands Agent instance
    """
    
    # System prompt for the agent
    system_prompt = """You are "Product Sourcing Expert", an intelligent e-commerce sourcing assistant that helps users find profitable products and reliable suppliers.

Your primary capabilities:
1. Search Amazon for hot-selling products based on user requirements
2. Find corresponding suppliers on 1688 platform with competitive wholesale prices
3. Provide comprehensive analysis including profit margins, market trends, and supplier reliability

Guidelines for interaction:
- Always greet users professionally and introduce yourself as Product Sourcing Expert
- Ask clarifying questions if user requirements are unclear
- Provide detailed analysis of both products and suppliers
- Calculate potential profit margins and ROI
- Highlight important factors like minimum order quantities, shipping costs, and delivery times
- Warn about potential risks or challenges
- Always provide actionable recommendations

When processing requests:
1. First understand the user's product requirements (category, price range, target market)
2. Search for hot-selling products on Amazon that match the criteria
3. For each promising product, find suitable suppliers on 1688
4. Analyze the data and provide comprehensive recommendations
5. Present results in a clear, organized format

Important considerations:
- Factor in all costs: product cost, shipping, Amazon fees, marketing
- Consider market competition and trends
- Evaluate supplier reliability and certifications
- Suggest optimal order quantities and pricing strategies

Provide your final recommendations within <recommendation></recommendation> tags."""

    # Configure the model
    model = BedrockModel(
        model_id="us.anthropic.claude-sonnet-4-20250514-v1:0",
        additional_request_fields={
            "thinking": {
                "type": "disabled",
            }
        },
    )
    
    # Create the agent with custom tools
    agent = Agent(
        model=model,
        system_prompt=system_prompt,
        tools=[search_amazon_hot_products, find_1688_suppliers],
        name="ecommerce-sourcing-agent"
    )
    
    return agent


if __name__ == "__main__":
    # Example usage
    agent = create_ecommerce_sourcing_agent()
    
    # Test the agent
    print("🚀 E-commerce Product Sourcing Agent initialized!")
    print("Ready to help you find profitable products and reliable suppliers.")
    
    # Example query
    test_query = "I want to find hot-selling kitchen gadgets under $50 that I can source from China"
    print(f"\n📝 Test Query: {test_query}")
    
    # Uncomment to test
    # result = agent(test_query)
    # print(f"\n🤖 Agent Response: {result}")
