"""
E-commerce Product Sourcing Agent

This agent helps users find hot-selling products on Amazon and corresponding suppliers on 1688 platform.
It provides comprehensive information including wholesale prices, supplier details, and shipping costs.

Architecture:
- Single agent using Strands framework
- Two custom tools: Amazon hot products API and 1688 supplier platform API
- Structured workflow for product sourcing and supplier matching
"""

import os
import json
from typing import Any, Dict, List, Optional
from dataclasses import dataclass

from strands import Agent, tool
from strands.models import BedrockModel
from strands.types.tools import ToolResult, ToolUse


@dataclass
class ProductInfo:
    """Data class for product information"""
    title: str
    price: float
    rating: float
    reviews_count: int
    image_url: str
    product_url: str
    category: str
    sales_rank: Optional[int] = None
    keywords: List[str] = None


@dataclass
class SupplierInfo:
    """Data class for supplier information"""
    supplier_name: str
    company_name: str
    wholesale_price: float
    minimum_order_quantity: int
    supplier_rating: float
    years_in_business: int
    location: str
    contact_info: Dict[str, str]
    product_images: List[str]
    shipping_cost: float
    delivery_time: str
    certifications: List[str] = None


# Tool 1: Amazon Hot Products API Integration
@tool
def search_amazon_hot_products(
    product_category: str,
    keywords: str,
    price_range_min: float = 0.0,
    price_range_max: float = 1000.0,
    min_rating: float = 4.0,
    max_results: int = 10
) -> List[Dict[str, Any]]:
    """
    Search for hot-selling products on Amazon based on user requirements.
    
    Args:
        product_category: Product category (e.g., "electronics", "home", "fashion")
        keywords: Search keywords describing the product
        price_range_min: Minimum price filter
        price_range_max: Maximum price filter
        min_rating: Minimum product rating filter
        max_results: Maximum number of results to return
    
    Returns:
        List of hot-selling products with details including price, rating, sales data
    """
    
    # Mock implementation - replace with actual Amazon API integration
    print(f"🔍 Searching Amazon for hot products: {keywords} in {product_category}")
    print(f"   Price range: ${price_range_min} - ${price_range_max}")
    print(f"   Minimum rating: {min_rating}")
    
    # Simulated hot products data
    mock_products = [
        {
            "title": f"Premium {keywords} - Best Seller",
            "price": 29.99,
            "rating": 4.5,
            "reviews_count": 2847,
            "image_url": "https://example.com/product1.jpg",
            "product_url": "https://amazon.com/product1",
            "category": product_category,
            "sales_rank": 1,
            "keywords": keywords.split(),
            "monthly_sales": 15000,
            "trend": "increasing",
            "competition_level": "medium"
        },
        {
            "title": f"Professional {keywords} Kit",
            "price": 45.99,
            "rating": 4.7,
            "reviews_count": 1923,
            "image_url": "https://example.com/product2.jpg",
            "product_url": "https://amazon.com/product2",
            "category": product_category,
            "sales_rank": 3,
            "keywords": keywords.split(),
            "monthly_sales": 12000,
            "trend": "stable",
            "competition_level": "low"
        },
        {
            "title": f"Deluxe {keywords} Set",
            "price": 19.99,
            "rating": 4.3,
            "reviews_count": 3456,
            "image_url": "https://example.com/product3.jpg",
            "product_url": "https://amazon.com/product3",
            "category": product_category,
            "sales_rank": 5,
            "keywords": keywords.split(),
            "monthly_sales": 8500,
            "trend": "increasing",
            "competition_level": "high"
        }
    ]
    
    # Filter by price range and rating
    filtered_products = [
        product for product in mock_products
        if (price_range_min <= product["price"] <= price_range_max and 
            product["rating"] >= min_rating)
    ]
    
    return filtered_products[:max_results]


# Tool 2: 1688 Supplier Platform API Integration
@tool
def find_1688_suppliers(
    product_title: str,
    target_price: float,
    minimum_order_qty: int = 100,
    supplier_location: str = "any",
    max_suppliers: int = 5
) -> List[Dict[str, Any]]:
    """
    Find suppliers on 1688 platform for the specified product.
    
    Args:
        product_title: Title/description of the product to source
        target_price: Target wholesale price per unit
        minimum_order_qty: Minimum order quantity requirement
        supplier_location: Preferred supplier location (e.g., "Guangdong", "Zhejiang", "any")
        max_suppliers: Maximum number of suppliers to return
    
    Returns:
        List of suppliers with wholesale prices, MOQ, ratings, and contact information
    """
    
    # Mock implementation - replace with actual 1688 API integration
    print(f"🏭 Searching 1688 suppliers for: {product_title}")
    print(f"   Target price: ${target_price}")
    print(f"   Minimum order quantity: {minimum_order_qty}")
    print(f"   Location preference: {supplier_location}")
    
    # Simulated supplier data
    mock_suppliers = [
        {
            "supplier_name": "Golden Dragon Manufacturing",
            "company_name": "Shenzhen Golden Dragon Electronics Co., Ltd.",
            "wholesale_price": target_price * 0.3,  # 30% of target price
            "minimum_order_quantity": 500,
            "supplier_rating": 4.8,
            "years_in_business": 12,
            "location": "Shenzhen, Guangdong",
            "contact_info": {
                "email": "<EMAIL>",
                "phone": "+86-755-12345678",
                "wechat": "GoldenDragon_Sales"
            },
            "product_images": [
                "https://1688.com/supplier1/image1.jpg",
                "https://1688.com/supplier1/image2.jpg"
            ],
            "shipping_cost": 2.50,
            "delivery_time": "15-20 days",
            "certifications": ["CE", "FCC", "RoHS"],
            "trade_assurance": True,
            "response_rate": "98%"
        },
        {
            "supplier_name": "Sunrise Industrial",
            "company_name": "Yiwu Sunrise Trading Co., Ltd.",
            "wholesale_price": target_price * 0.35,  # 35% of target price
            "minimum_order_quantity": 300,
            "supplier_rating": 4.6,
            "years_in_business": 8,
            "location": "Yiwu, Zhejiang",
            "contact_info": {
                "email": "<EMAIL>",
                "phone": "+86-579-87654321",
                "wechat": "Sunrise_Export"
            },
            "product_images": [
                "https://1688.com/supplier2/image1.jpg",
                "https://1688.com/supplier2/image2.jpg"
            ],
            "shipping_cost": 3.00,
            "delivery_time": "12-18 days",
            "certifications": ["CE", "ISO9001"],
            "trade_assurance": True,
            "response_rate": "95%"
        },
        {
            "supplier_name": "Phoenix Manufacturing",
            "company_name": "Dongguan Phoenix Electronics Factory",
            "wholesale_price": target_price * 0.28,  # 28% of target price
            "minimum_order_quantity": 1000,
            "supplier_rating": 4.9,
            "years_in_business": 15,
            "location": "Dongguan, Guangdong",
            "contact_info": {
                "email": "<EMAIL>",
                "phone": "+86-769-98765432",
                "wechat": "Phoenix_Global"
            },
            "product_images": [
                "https://1688.com/supplier3/image1.jpg",
                "https://1688.com/supplier3/image2.jpg"
            ],
            "shipping_cost": 2.20,
            "delivery_time": "10-15 days",
            "certifications": ["CE", "FCC", "RoHS", "ISO9001"],
            "trade_assurance": True,
            "response_rate": "99%"
        }
    ]
    
    # Filter suppliers based on criteria
    filtered_suppliers = [
        supplier for supplier in mock_suppliers
        if (supplier["minimum_order_quantity"] <= minimum_order_qty * 2 and  # Allow some flexibility
            (supplier_location == "any" or supplier_location.lower() in supplier["location"].lower()))
    ]
    
    # Sort by rating and price
    filtered_suppliers.sort(key=lambda x: (-x["supplier_rating"], x["wholesale_price"]))
    
    return filtered_suppliers[:max_suppliers]


def create_ecommerce_sourcing_agent() -> Agent:
    """
    Create and configure the e-commerce product sourcing agent.
    
    Returns:
        Configured Strands Agent instance
    """
    
    # System prompt for the agent
    system_prompt = """You are "Product Sourcing Expert", an intelligent e-commerce sourcing assistant that helps users find profitable products and reliable suppliers.

Your primary capabilities:
1. Search Amazon for hot-selling products based on user requirements
2. Find corresponding suppliers on 1688 platform with competitive wholesale prices
3. Provide comprehensive analysis including profit margins, market trends, and supplier reliability

Guidelines for interaction:
- Always greet users professionally and introduce yourself as Product Sourcing Expert
- Ask clarifying questions if user requirements are unclear
- Provide detailed analysis of both products and suppliers
- Calculate potential profit margins and ROI
- Highlight important factors like minimum order quantities, shipping costs, and delivery times
- Warn about potential risks or challenges
- Always provide actionable recommendations

When processing requests:
1. First understand the user's product requirements (category, price range, target market)
2. Search for hot-selling products on Amazon that match the criteria
3. For each promising product, find suitable suppliers on 1688
4. Analyze the data and provide comprehensive recommendations
5. Present results in a clear, organized format

Important considerations:
- Factor in all costs: product cost, shipping, Amazon fees, marketing
- Consider market competition and trends
- Evaluate supplier reliability and certifications
- Suggest optimal order quantities and pricing strategies

Provide your final recommendations within <recommendation></recommendation> tags."""

    # Configure the model
    model = BedrockModel(
        model_id="us.anthropic.claude-sonnet-4-20250514-v1:0",
        additional_request_fields={
            "thinking": {
                "type": "disabled",
            }
        },
    )
    
    # Create the agent with custom tools
    agent = Agent(
        model=model,
        system_prompt=system_prompt,
        tools=[search_amazon_hot_products, find_1688_suppliers],
        name="ecommerce-sourcing-agent"
    )
    
    return agent


if __name__ == "__main__":
    # Example usage
    agent = create_ecommerce_sourcing_agent()
    
    # Test the agent
    print("🚀 E-commerce Product Sourcing Agent initialized!")
    print("Ready to help you find profitable products and reliable suppliers.")
    
    # Example query
    test_query = "I want to find hot-selling kitchen gadgets under $50 that I can source from China"
    print(f"\n📝 Test Query: {test_query}")
    
    # Uncomment to test
    # result = agent(test_query)
    # print(f"\n🤖 Agent Response: {result}")
