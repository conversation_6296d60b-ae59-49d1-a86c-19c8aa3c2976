# E-commerce Product Sourcing Agent

An intelligent agent built with the Strands framework that helps users find profitable products on Amazon and corresponding suppliers on 1688 platform for e-commerce businesses.

## 🎯 Overview

This agent streamlines the product sourcing process by:
- Searching Amazon for hot-selling products based on user criteria
- Finding reliable suppliers on 1688 platform with competitive wholesale prices
- Providing comprehensive analysis including profit margins, market trends, and supplier reliability
- Delivering actionable recommendations for e-commerce success

## 🏗️ Architecture

### Single Agent Design
- **Framework**: Strands with Amazon Bedrock
- **Model**: Anthropic Claude 3.7 Sonnet
- **Tools**: 2 custom tools for API integrations
- **Workflow**: Structured product research → supplier sourcing → profit analysis

### Custom Tools

#### 1. Amazon Hot Products Search (`search_amazon_hot_products`)
- Searches for trending products on Amazon
- Filters by category, price range, ratings, and sales volume
- Returns product details including pricing, reviews, and market trends

#### 2. 1688 Supplier Search (`find_1688_suppliers`)
- Finds suppliers on 1688 platform for specified products
- Filters by wholesale price, minimum order quantity, and location
- Returns supplier details including contact info, certifications, and shipping costs

## 🚀 Quick Start

### Prerequisites
- Python 3.9+
- AWS account with Bedrock access
- Strands framework installed

### Installation

1. **Clone or download the project files**
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure AWS credentials** for Bedrock access

4. **Run the demo**:
   ```python
   from ecommerce_sourcing_agent import create_ecommerce_sourcing_agent
   
   # Initialize the agent
   agent = create_ecommerce_sourcing_agent()
   
   # Use the agent
   result = agent("I want to find hot-selling kitchen gadgets under $50")
   print(result)
   ```

## 📊 Features

### Core Functionality
- ✅ Product trend analysis
- ✅ Supplier verification and rating
- ✅ Profit margin calculations
- ✅ Market competition assessment
- ✅ Shipping cost analysis
- ✅ Minimum order quantity optimization

### Data Analysis
- **Profit Calculations**: Automatic ROI and margin analysis
- **Market Trends**: Product popularity and sales velocity
- **Supplier Reliability**: Ratings, certifications, and trade assurance
- **Cost Breakdown**: Complete cost structure including fees and shipping

### User Experience
- **Natural Language Interface**: Conversational product sourcing
- **Structured Recommendations**: Clear, actionable insights
- **Risk Assessment**: Potential challenges and mitigation strategies
- **Flexible Queries**: Support for various product categories and criteria

## 🛠️ Usage Examples

### Example 1: Kitchen Gadgets
```python
query = """
I want to start an Amazon FBA business selling kitchen gadgets. 
I'm looking for products under $50 that are trending and have good profit potential. 
I can invest in an initial order of 1000 units.
"""

result = agent(query)
```

### Example 2: Electronics Accessories
```python
query = """
I'm interested in sourcing phone accessories, specifically wireless chargers. 
My budget is $20-100 per unit retail price. 
I want products with at least 4.5 star ratings and high sales volume.
"""

result = agent(query)
```

### Example 3: Home & Garden
```python
query = """
I want to explore seasonal home and garden products. 
Price range $15-75. I prefer suppliers with trade assurance and fast delivery.
"""

result = agent(query)
```

## 📁 Project Structure

```
ecommerce-sourcing-agent/
├── ecommerce_sourcing_agent.py    # Main agent implementation
├── demo.ipynb                     # Interactive demo notebook
├── requirements.txt               # Python dependencies
├── README.md                      # This file
└── tools/                         # Tool implementations (future)
    ├── amazon_api.py             # Real Amazon API integration
    └── alibaba_1688_api.py       # Real 1688 API integration
```

## 🔧 Current Implementation Status

### ✅ Completed
- [x] Strands agent framework setup
- [x] Two custom tools with mock implementations
- [x] Agent workflow and conversation handling
- [x] Data structures for products and suppliers
- [x] Basic profit analysis functionality
- [x] Demo notebook with examples
- [x] Comprehensive documentation

### 🚧 Next Steps (Ready for API Integration)
- [ ] Real Amazon API integration
- [ ] Real 1688 platform API integration
- [ ] Enhanced error handling and validation
- [ ] Rate limiting and API quota management
- [ ] Advanced market analysis features
- [ ] Data persistence and caching
- [ ] User preference management

## 🔌 API Integration Guide

The agent is designed with clear interfaces for easy API integration:

### Amazon API Integration
Replace the mock implementation in `search_amazon_hot_products()` with:
- Amazon Product Advertising API
- Amazon MWS API
- Third-party Amazon data services

### 1688 API Integration
Replace the mock implementation in `find_1688_suppliers()` with:
- Alibaba Open Platform API
- 1688 supplier directory APIs
- Third-party sourcing platforms

### Integration Points
```python
# Current mock structure - ready for real API calls
def search_amazon_hot_products(product_category, keywords, ...):
    # TODO: Replace with actual Amazon API calls
    # return real_amazon_api.search_products(...)
    pass

def find_1688_suppliers(product_title, target_price, ...):
    # TODO: Replace with actual 1688 API calls
    # return real_1688_api.find_suppliers(...)
    pass
```

## 📈 Profit Analysis

The agent includes built-in profit analysis considering:
- **Product Costs**: Wholesale price + shipping
- **Amazon Fees**: Referral fees + FBA fees
- **Profit Margins**: Gross profit percentage
- **ROI Calculations**: Return on investment analysis
- **Break-even Analysis**: Minimum sales requirements

## 🤝 Contributing

This is a framework ready for extension. Key areas for contribution:
1. Real API integrations
2. Enhanced data analysis
3. Additional sourcing platforms
4. Market trend analysis
5. User interface improvements

## 📄 License

This project is provided as a framework example for educational and development purposes.

## 🆘 Support

For questions about:
- **Strands Framework**: Refer to Strands documentation
- **Agent Implementation**: Check the demo notebook
- **API Integration**: See integration guide above
- **Customization**: Modify tools and prompts as needed

---

**Ready to revolutionize your e-commerce sourcing process!** 🚀

This framework provides the foundation - add your API integrations to unlock the full potential.
