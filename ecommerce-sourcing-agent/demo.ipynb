{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# E-commerce Product Sourcing Agent Demo\n", "\n", "## Overview\n", "This notebook demonstrates the E-commerce Product Sourcing Agent built with the Strands framework. The agent helps users find hot-selling products on Amazon and corresponding suppliers on 1688 platform.\n", "\n", "## Agent Features\n", "\n", "| Feature | Description |\n", "|---------|-------------|\n", "| Custom tools created | search_amazon_hot_products, find_1688_suppliers |\n", "| Agent Structure | Single agent architecture |\n", "| Core Functionality | Product research, supplier sourcing, profit analysis |\n", "| Framework | Strands with Amazon Bedrock |\n", "\n", "## Architecture\n", "\n", "The agent follows a structured workflow:\n", "1. **User Input**: Product requirements and criteria\n", "2. **Amazon Search**: Find hot-selling products matching criteria\n", "3. **Supplier Search**: Find corresponding suppliers on 1688\n", "4. **Analysis**: Calculate profit margins and provide recommendations\n", "5. **Output**: Comprehensive sourcing report with actionable insights"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Installation\n", "\n", "First, let's install the required dependencies:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install requirements\n", "!pip install -r requirements.txt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Dependencies\n", "\n", "Import the necessary packages and our custom agent:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "from pprint import pprint\n", "\n", "# Import our custom agent\n", "from ecommerce_sourcing_agent import (\n", "    create_ecommerce_sourcing_agent,\n", "    search_amazon_hot_products,\n", "    find_1688_suppliers\n", ")\n", "\n", "# Import Strands components\n", "from strands import Agent\n", "from strands.models import BedrockModel"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize the Agent\n", "\n", "Create and configure the e-commerce sourcing agent:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create the agent\n", "agent = create_ecommerce_sourcing_agent()\n", "\n", "print(\"🚀 E-commerce Product Sourcing Agent initialized!\")\n", "print(\"Agent Name:\", agent.name)\n", "print(\"Available Tools:\", [tool.name for tool in agent.tools])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test Individual Tools\n", "\n", "Let's test each tool individually to understand their functionality:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Test Amazon Hot Products Search"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test Amazon product search\n", "print(\"Testing Amazon Hot Products Search...\")\n", "amazon_results = search_amazon_hot_products(\n", "    product_category=\"kitchen\",\n", "    keywords=\"silicone cooking utensils\",\n", "    price_range_min=10.0,\n", "    price_range_max=50.0,\n", "    min_rating=4.0,\n", "    max_results=3\n", ")\n", "\n", "print(\"\\n📊 Amazon Search Results:\")\n", "for i, product in enumerate(amazon_results, 1):\n", "    print(f\"\\n{i}. {product['title']}\")\n", "    print(f\"   Price: ${product['price']}\")\n", "    print(f\"   Rating: {product['rating']} ({product['reviews_count']} reviews)\")\n", "    print(f\"   Monthly Sales: {product['monthly_sales']}\")\n", "    print(f\"   Trend: {product['trend']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Test 1688 Supplier Search"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 1688 supplier search\n", "print(\"Testing 1688 Supplier Search...\")\n", "supplier_results = find_1688_suppliers(\n", "    product_title=\"silicone cooking utensils set\",\n", "    target_price=25.0,\n", "    minimum_order_qty=500,\n", "    supplier_location=\"Guangdong\",\n", "    max_suppliers=3\n", ")\n", "\n", "print(\"\\n🏭 Supplier Search Results:\")\n", "for i, supplier in enumerate(supplier_results, 1):\n", "    print(f\"\\n{i}. {supplier['supplier_name']}\")\n", "    print(f\"   Company: {supplier['company_name']}\")\n", "    print(f\"   Wholesale Price: ${supplier['wholesale_price']:.2f}\")\n", "    print(f\"   MOQ: {supplier['minimum_order_quantity']} units\")\n", "    print(f\"   Rating: {supplier['supplier_rating']} ({supplier['years_in_business']} years)\")\n", "    print(f\"   Location: {supplier['location']}\")\n", "    print(f\"   Delivery: {supplier['delivery_time']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Agent Interaction Examples\n", "\n", "Now let's test the full agent workflow with different scenarios:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Example 1: Kitchen Gadgets Sourcing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query1 = \"\"\"\n", "I want to start an Amazon FBA business selling kitchen gadgets. \n", "I'm looking for products under $50 that are trending and have good profit potential. \n", "I can invest in an initial order of 1000 units. \n", "Please help me find suitable products and suppliers.\n", "\"\"\"\n", "\n", "print(\"🔍 Query 1: Kitchen Gadgets Sourcing\")\n", "print(\"User:\", query1.strip())\n", "print(\"\\n\" + \"=\"*80)\n", "\n", "# Uncomment to run with actual agent\n", "# result1 = agent(query1)\n", "# print(\"\\n🤖 Agent Response:\")\n", "# print(result1)\n", "\n", "print(\"\\n[Note: Uncomment the agent call above to see the full response]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Example 2: Electronics Sourcing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query2 = \"\"\"\n", "I'm interested in sourcing phone accessories, specifically wireless chargers and phone cases. \n", "My budget is $20-100 per unit retail price. \n", "I want to focus on products with at least 4.5 star ratings and high sales volume. \n", "Can you find me the best opportunities?\n", "\"\"\"\n", "\n", "print(\"🔍 Query 2: Electronics Sourcing\")\n", "print(\"User:\", query2.strip())\n", "print(\"\\n\" + \"=\"*80)\n", "\n", "# Uncomment to run with actual agent\n", "# result2 = agent(query2)\n", "# print(\"\\n🤖 Agent Response:\")\n", "# print(result2)\n", "\n", "print(\"\\n[Note: Uncomment the agent call above to see the full response]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Example 3: Home & Garden Products"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query3 = \"\"\"\n", "I want to explore the home and garden category. \n", "Looking for seasonal products that are currently trending. \n", "Price range $15-75. I prefer suppliers with trade assurance and fast delivery. \n", "What are the best opportunities right now?\n", "\"\"\"\n", "\n", "print(\"🔍 Query 3: Home & Garden Products\")\n", "print(\"User:\", query3.strip())\n", "print(\"\\n\" + \"=\"*80)\n", "\n", "# Uncomment to run with actual agent\n", "# result3 = agent(query3)\n", "# print(\"\\n🤖 Agent Response:\")\n", "# print(result3)\n", "\n", "print(\"\\n[Note: Uncomment the agent call above to see the full response]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Profit Analysis Helper\n", "\n", "Let's create a helper function to analyze profit potential:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_profit_potential(amazon_price, wholesale_price, shipping_cost, order_quantity):\n", "    \"\"\"\n", "    Calculate profit potential for a product sourcing opportunity.\n", "    \"\"\"\n", "    # Amazon fees (approximate)\n", "    amazon_referral_fee = amazon_price * 0.15  # 15% average\n", "    amazon_fba_fee = 3.50  # Approximate FBA fee\n", "    \n", "    # Total costs per unit\n", "    cost_per_unit = wholesale_price + shipping_cost\n", "    amazon_fees_per_unit = amazon_referral_fee + amazon_fba_fee\n", "    \n", "    # Profit calculations\n", "    gross_profit_per_unit = amazon_price - cost_per_unit - amazon_fees_per_unit\n", "    profit_margin = (gross_profit_per_unit / amazon_price) * 100\n", "    total_investment = cost_per_unit * order_quantity\n", "    potential_revenue = amazon_price * order_quantity\n", "    total_profit = gross_profit_per_unit * order_quantity\n", "    roi = (total_profit / total_investment) * 100\n", "    \n", "    return {\n", "        \"cost_per_unit\": cost_per_unit,\n", "        \"amazon_fees_per_unit\": amazon_fees_per_unit,\n", "        \"gross_profit_per_unit\": gross_profit_per_unit,\n", "        \"profit_margin_percent\": profit_margin,\n", "        \"total_investment\": total_investment,\n", "        \"potential_revenue\": potential_revenue,\n", "        \"total_profit\": total_profit,\n", "        \"roi_percent\": roi\n", "    }\n", "\n", "# Example profit analysis\n", "print(\"📊 Profit Analysis Example:\")\n", "analysis = analyze_profit_potential(\n", "    amazon_price=29.99,\n", "    wholesale_price=7.50,\n", "    shipping_cost=2.50,\n", "    order_quantity=1000\n", ")\n", "\n", "print(f\"Amazon Selling Price: ${29.99}\")\n", "print(f\"Cost per Unit: ${analysis['cost_per_unit']:.2f}\")\n", "print(f\"Amazon Fees per Unit: ${analysis['amazon_fees_per_unit']:.2f}\")\n", "print(f\"Gross Profit per Unit: ${analysis['gross_profit_per_unit']:.2f}\")\n", "print(f\"Profit Margin: {analysis['profit_margin_percent']:.1f}%\")\n", "print(f\"Total Investment: ${analysis['total_investment']:,.2f}\")\n", "print(f\"Potential Revenue: ${analysis['potential_revenue']:,.2f}\")\n", "print(f\"Total Profit: ${analysis['total_profit']:,.2f}\")\n", "print(f\"ROI: {analysis['roi_percent']:.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next Steps\n", "\n", "To make this agent production-ready, you'll need to:\n", "\n", "1. **Implement Real API Integrations**:\n", "   - Replace mock data with actual Amazon API calls\n", "   - Integrate with 1688 platform APIs\n", "   - Add error handling and rate limiting\n", "\n", "2. **Enhance Data Analysis**:\n", "   - Add market trend analysis\n", "   - Implement competitor analysis\n", "   - Include seasonal demand patterns\n", "\n", "3. **Add More Features**:\n", "   - Product image analysis\n", "   - Supplier verification\n", "   - Automated profit calculations\n", "   - Risk assessment\n", "\n", "4. **Improve User Experience**:\n", "   - Add data visualization\n", "   - Create export functionality\n", "   - Implement user preferences\n", "   - Add conversation memory\n", "\n", "The current framework provides a solid foundation that can be easily extended with these enhancements."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}