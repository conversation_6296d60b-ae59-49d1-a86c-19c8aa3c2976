#!/usr/bin/env python3
"""
Test script for the E-commerce Product Sourcing Agent

This script validates the agent framework and demonstrates basic functionality
without requiring full AWS/Bedrock setup for initial testing.
"""

import sys
import json
from typing import Dict, Any

def test_tool_imports():
    """Test that all tools can be imported successfully"""
    try:
        from ecommerce_sourcing_agent import (
            search_amazon_hot_products,
            find_1688_suppliers,
            create_ecommerce_sourcing_agent,
            ProductInfo,
            SupplierInfo
        )
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_amazon_tool():
    """Test the Amazon hot products search tool"""
    try:
        from ecommerce_sourcing_agent import search_amazon_hot_products
        
        print("\n🔍 Testing Amazon Hot Products Tool...")
        results = search_amazon_hot_products(
            product_category="electronics",
            keywords="bluetooth headphones",
            price_range_min=20.0,
            price_range_max=100.0,
            min_rating=4.0,
            max_results=2
        )
        
        print(f"✅ Amazon tool returned {len(results)} products")
        
        # Validate result structure
        if results and isinstance(results, list):
            product = results[0]
            required_fields = ['title', 'price', 'rating', 'reviews_count', 'category']
            missing_fields = [field for field in required_fields if field not in product]
            
            if not missing_fields:
                print("✅ Product data structure is valid")
                print(f"   Sample product: {product['title'][:50]}...")
                return True
            else:
                print(f"❌ Missing fields in product data: {missing_fields}")
                return False
        else:
            print("❌ Invalid results format")
            return False
            
    except Exception as e:
        print(f"❌ Amazon tool test failed: {e}")
        return False

def test_1688_tool():
    """Test the 1688 supplier search tool"""
    try:
        from ecommerce_sourcing_agent import find_1688_suppliers
        
        print("\n🏭 Testing 1688 Supplier Tool...")
        results = find_1688_suppliers(
            product_title="bluetooth headphones",
            target_price=50.0,
            minimum_order_qty=500,
            supplier_location="any",
            max_suppliers=2
        )
        
        print(f"✅ 1688 tool returned {len(results)} suppliers")
        
        # Validate result structure
        if results and isinstance(results, list):
            supplier = results[0]
            required_fields = ['supplier_name', 'wholesale_price', 'minimum_order_quantity', 'supplier_rating']
            missing_fields = [field for field in required_fields if field not in supplier]
            
            if not missing_fields:
                print("✅ Supplier data structure is valid")
                print(f"   Sample supplier: {supplier['supplier_name']}")
                return True
            else:
                print(f"❌ Missing fields in supplier data: {missing_fields}")
                return False
        else:
            print("❌ Invalid results format")
            return False
            
    except Exception as e:
        print(f"❌ 1688 tool test failed: {e}")
        return False

def test_data_classes():
    """Test the data class definitions"""
    try:
        from ecommerce_sourcing_agent import ProductInfo, SupplierInfo
        
        print("\n📊 Testing Data Classes...")
        
        # Test ProductInfo
        product = ProductInfo(
            title="Test Product",
            price=29.99,
            rating=4.5,
            reviews_count=1000,
            image_url="https://example.com/image.jpg",
            product_url="https://example.com/product",
            category="test"
        )
        print("✅ ProductInfo class works correctly")
        
        # Test SupplierInfo
        supplier = SupplierInfo(
            supplier_name="Test Supplier",
            company_name="Test Company Ltd.",
            wholesale_price=10.99,
            minimum_order_quantity=500,
            supplier_rating=4.8,
            years_in_business=10,
            location="Test City",
            contact_info={"email": "<EMAIL>"},
            product_images=["https://example.com/img1.jpg"],
            shipping_cost=2.50,
            delivery_time="15-20 days"
        )
        print("✅ SupplierInfo class works correctly")
        return True
        
    except Exception as e:
        print(f"❌ Data class test failed: {e}")
        return False

def test_agent_creation():
    """Test agent creation (without actually invoking it)"""
    try:
        from ecommerce_sourcing_agent import create_ecommerce_sourcing_agent
        
        print("\n🤖 Testing Agent Creation...")
        
        # This will test the agent creation without requiring AWS credentials
        # We'll catch the expected AWS credential error
        try:
            agent = create_ecommerce_sourcing_agent()
            print("✅ Agent created successfully")
            print(f"   Agent name: {agent.name}")
            # Check if agent has tools attribute (different Strands versions may vary)
            if hasattr(agent, 'tools'):
                print(f"   Number of tools: {len(agent.tools)}")
            elif hasattr(agent, '_tools'):
                print(f"   Number of tools: {len(agent._tools)}")
            else:
                print("   Tools attribute not accessible (this is normal)")
            return True
        except Exception as e:
            if "credentials" in str(e).lower() or "aws" in str(e).lower():
                print("✅ Agent framework is correct (AWS credentials needed for full functionality)")
                return True
            else:
                print(f"❌ Agent creation failed: {e}")
                return False
                
    except Exception as e:
        print(f"❌ Agent creation test failed: {e}")
        return False

def run_profit_analysis_test():
    """Test the profit analysis functionality"""
    try:
        print("\n💰 Testing Profit Analysis...")
        
        # Simple profit calculation
        amazon_price = 49.99
        wholesale_price = 12.50
        shipping_cost = 3.00
        order_quantity = 1000
        
        # Amazon fees (approximate)
        amazon_referral_fee = amazon_price * 0.15
        amazon_fba_fee = 3.50
        
        cost_per_unit = wholesale_price + shipping_cost
        amazon_fees_per_unit = amazon_referral_fee + amazon_fba_fee
        gross_profit_per_unit = amazon_price - cost_per_unit - amazon_fees_per_unit
        profit_margin = (gross_profit_per_unit / amazon_price) * 100
        
        print(f"✅ Profit analysis calculation successful")
        print(f"   Amazon Price: ${amazon_price}")
        print(f"   Cost per Unit: ${cost_per_unit:.2f}")
        print(f"   Gross Profit: ${gross_profit_per_unit:.2f}")
        print(f"   Profit Margin: {profit_margin:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Profit analysis test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 E-commerce Product Sourcing Agent - Test Suite")
    print("=" * 60)
    
    tests = [
        ("Tool Imports", test_tool_imports),
        ("Amazon Tool", test_amazon_tool),
        ("1688 Tool", test_1688_tool),
        ("Data Classes", test_data_classes),
        ("Agent Creation", test_agent_creation),
        ("Profit Analysis", run_profit_analysis_test)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} - {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The agent framework is ready for API integration.")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
